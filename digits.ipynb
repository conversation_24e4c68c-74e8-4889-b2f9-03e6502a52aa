{"cells": [{"cell_type": "markdown", "id": "6be5ca29", "metadata": {}, "source": ["# Handwritten Digit Recognition\n", "\n", "Use the MNIST dataset"]}, {"cell_type": "code", "execution_count": null, "id": "751954c7", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader\n", "from torchvision import datasets, transforms\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "markdown", "id": "c4a69d9b", "metadata": {}, "source": ["## Data Preparation"]}, {"cell_type": "code", "execution_count": null, "id": "ca88d075", "metadata": {}, "outputs": [], "source": ["T = transforms.ToTensor()\n", "train = datasets.MNIST(root=\"./Data\", train=True, download=True, transform=T)\n", "train"]}, {"cell_type": "code", "execution_count": null, "id": "827a491d", "metadata": {}, "outputs": [], "source": ["test = datasets.MNIST(root=\"./Data\", train=False, download=True, transform=T)\n", "test"]}, {"cell_type": "code", "execution_count": null, "id": "4108eb52", "metadata": {}, "outputs": [], "source": ["# Example of an image\n", "image, label = train[1]\n", "print(\"Shape: \", image.shape, \"\\nLabel: \", label)\n", "plt.imshow(image.reshape((28,28)), cmap=\"gray\")"]}, {"cell_type": "markdown", "id": "76421b11", "metadata": {}, "source": ["## Network Model"]}, {"cell_type": "code", "execution_count": null, "id": "c2cd27c8", "metadata": {}, "outputs": [], "source": ["class MLP(nn.Mo<PERSON>le):\n", "    \n", "    def __init__(self):\n", "        super().__init__()\n", "        self.d1 = nn.<PERSON><PERSON>(784, 250)    # Hidden Layer 1\n", "        self.d2 = nn.<PERSON>ar(250, 100)     # Hidden Layer 2\n", "        self.d3 = nn.<PERSON><PERSON>(100, 10)   # Output Layer\n", "        \n", "    def forward(self, x):\n", "        x = <PERSON>.relu(self.d1(x))\n", "        x = <PERSON>.relu(self.d2(x))\n", "        x = self.d3(x)\n", "        return F.log_softmax(x, dim=1)"]}, {"cell_type": "code", "execution_count": null, "id": "d011b7b5", "metadata": {}, "outputs": [], "source": ["model = MLP()\n", "print(model)"]}, {"cell_type": "code", "execution_count": null, "id": "a9c1d442", "metadata": {}, "outputs": [], "source": ["loss_fn = nn.CrossEntropyLoss()\n", "optimizer = torch.optim.Adam(model.parameters(), lr=0.001)"]}, {"cell_type": "markdown", "id": "bc275c42", "metadata": {}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "id": "d88cb18d", "metadata": {}, "outputs": [], "source": ["b_size = 32\n", "train_loader = DataLoader(train, batch_size=b_size, shuffle=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0df03925", "metadata": {}, "outputs": [], "source": ["# Flatten the image to 1-D\n", "for images, labels in train_loader:\n", "    print(\"Initial batch shape: \", image.size())\n", "    break\n", "print(\"Batch shape after flattening: \", images.view(b_size,-1).size())"]}, {"cell_type": "code", "execution_count": null, "id": "00f05ae3", "metadata": {}, "outputs": [], "source": ["# Generates a progress bar\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "83204373", "metadata": {}, "outputs": [], "source": ["epochs = 4\n", "train_losses = []\n", "train_correct = []"]}, {"cell_type": "code", "execution_count": null, "id": "f9f21cd3", "metadata": {}, "outputs": [], "source": ["def train_model(model, optimizer, data_loader, loss_module, num_epochs=100):\n", "    model.train()\n", "    \n", "    for i in tqdm(range(num_epochs)):\n", "        trn_corr = 0\n", "        \n", "        for b, (x_train, y_train) in enumerate(data_loader):\n", "            \n", "            # Compute output and loss\n", "            y_pred = model(x_train.view(b_size, -1))\n", "            loss = loss_module(y_pred, y_train)\n", "            \n", "            # Update weights\n", "            optimizer.zero_grad()\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "             # Calculate number of correct predictions\n", "            predicted = torch.max(y_pred.data, 1)[1]    # the prediction with the highest probability\n", "            batch_corr = (predicted == y_train).sum()\n", "            trn_corr += batch_corr\n", "            \n", "            if b % 50 == 0:\n", "                print('Epoch : {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}'.format(\n", "                    i, b*b_size, len(data_loader.dataset), 100.*b / len(data_loader), loss.data))\n", "            \n", "        # Update training loss and accuracy for the epoch\n", "        train_losses.append(loss.item())\n", "        train_correct.append(trn_corr)"]}, {"cell_type": "code", "execution_count": null, "id": "9060f50c", "metadata": {}, "outputs": [], "source": ["train_model(model, optimizer, train_loader, loss_fn, num_epochs=epochs)"]}, {"cell_type": "markdown", "id": "b2916aca", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "52b9cce6", "metadata": {}, "outputs": [], "source": ["test_losses = []\n", "test_correct = []\n", "test_loader = DataLoader(test, batch_size=500, shuffle=False)"]}, {"cell_type": "code", "execution_count": null, "id": "55677340", "metadata": {}, "outputs": [], "source": ["def eval_model(model, dataloader, loss_module):\n", "    model.eval()\n", "    tst_corr = 0\n", "    \n", "    with torch.no_grad():\n", "        for b, (x_test, y_test) in enumerate(dataloader):\n", "            y_val = model(x_test.view(500, -1))\n", "            predicted = torch.max(y_val.data, 1)[1]\n", "            tst_corr += (predicted == y_test).sum()\n", "            \n", "            loss = loss_module(y_val, y_test)\n", "            test_losses.append(loss)\n", "            test_correct.append(tst_corr)"]}, {"cell_type": "code", "execution_count": null, "id": "d582bf94", "metadata": {}, "outputs": [], "source": ["eval_model(model, test_loader, loss_fn)\n", "print(f'Test accuracy: {test_correct[-1].item()*100/10000:.3f}%')"]}], "metadata": {"kernelspec": {"display_name": "comp815", "language": "python", "name": "comp815"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}