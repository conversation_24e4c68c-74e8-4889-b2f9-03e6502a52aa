{"cells": [{"cell_type": "markdown", "id": "066a7df8", "metadata": {}, "source": ["# Read n-MNIST csv File"]}, {"cell_type": "code", "execution_count": 3, "id": "c0916e20", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import csv\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "id": "92195123", "metadata": {}, "source": ["### Read the training data"]}, {"cell_type": "code", "execution_count": 4, "id": "d0d8d3db", "metadata": {}, "outputs": [], "source": ["with open('./n-MNIST/trainx.csv', 'r') as csv_file:\n", "    csvreader = csv.reader(csv_file)\n", "    for data in csvreader:\n", "        img = np.array(data, dtype='int64')"]}, {"cell_type": "markdown", "id": "0d8eeaf0", "metadata": {}, "source": ["### Each image is now a linear numpy array"]}, {"cell_type": "code", "execution_count": 5, "id": "5afc1526", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(784,)\n"]}], "source": ["print(img.shape)"]}, {"cell_type": "markdown", "id": "7c2a1369", "metadata": {}, "source": ["### Take the last image and reshape it to a square 2D array"]}, {"cell_type": "code", "execution_count": 6, "id": "69898077", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(28, 28)\n"]}], "source": ["pixels = img.reshape((28,28))\n", "print(pixels.shape)"]}, {"cell_type": "markdown", "id": "9e193f71", "metadata": {}, "source": ["### Display this image"]}, {"cell_type": "code", "execution_count": 7, "id": "f82dc262", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAaEAAAGdCAYAAAC7EMwUAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAc9UlEQVR4nO3db2yV9f3/8dcByqHg6dFa2nNqsdYF5iKMBXT8mSK60dlsTMQlqMlSksXoBBKCxoyRxc4b1JhIvMF0mVkYZDK5MWUuErULtrgxNiQYEJUVKevBtpZWOKeUcgr087tB7PdX+SOfi3P67mmfj+RKONe5XlyfXlz01avnOp8Tcs45AQBgYJT1AAAAIxclBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADNjrAfwVX19fWppaVEkElEoFLIeDgDAk3NOXV1dKi0t1ahRl7/WGXIl1NLSokmTJlkPAwBwlRKJhMrKyi67zZD7dVwkErEeAgAgA67k+3nWSujFF19URUWFxo0bp5kzZ+q99967ohy/ggOA4eFKvp9npYS2bNmilStXas2aNdq7d6/uvPNOVVVVqbm5ORu7AwDkqFA2ZtGeNWuWZsyYoZdeeql/3be+9S0tWrRItbW1l82mUilFo9FMDwkAMMiSyaQKCgouu03Gr4R6e3u1Z88eVVZWDlhfWVmpnTt3XrB9Op1WKpUasAAARoaMl1BHR4fOnTunkpKSAetLSkrU1tZ2wfa1tbWKRqP9C3fGAcDIkbUbE776gpRz7qIvUq1evVrJZLJ/SSQS2RoSAGCIyfj7hIqKijR69OgLrnra29svuDqSpHA4rHA4nOlhAAByQMavhMaOHauZM2eqrq5uwPq6ujrNnTs307sDAOSwrMyYsGrVKv3sZz/Tbbfdpjlz5uj3v/+9mpub9dhjj2VjdwCAHJWVElqyZIk6Ozv1zDPPqLW1VVOnTtW2bdtUXl6ejd0BAHJUVt4ndDV4nxAADA8m7xMCAOBKUUIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADNjrAcAIHvGjRsXKFdQUOCdGTt2bKB9+err6/POJJPJQPvq7u4OlMOV40oIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGSYwDSA/P987c/3113tnTp8+7Z3p6OjwzuDqjBnj/99owoQJ3pnrrrvOO1NeXu6dkaSJEyd6Z/Ly8gLty9eZM2e8M0eOHAm0r0OHDnlnTpw4EWhfIxVXQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMwwgWkApaWl3pmbb77ZO7Nv3z7vDK7O6NGjvTM33XSTd+ab3/ymdyYWi3lnCgsLvTOSdPbsWe/MyZMnvTNBJukdN26cdyboRK5B/Pe///XOpFKpLIwkN3AlBAAwQwkBAMxkvIRqamoUCoUGLEF+jQAAGP6y8prQrbfeqr///e/9j4P8nh0AMPxlpYTGjBnD1Q8A4Gtl5TWhxsZGlZaWqqKiQg8++KAOHz58yW3T6bRSqdSABQAwMmS8hGbNmqVNmzbp7bff1ssvv6y2tjbNnTtXnZ2dF92+trZW0Wi0f5k0aVKmhwQAGKIyXkJVVVV64IEHNG3aNP3gBz/Qm2++KUnauHHjRbdfvXq1kslk/5JIJDI9JADAEJX1N6tOmDBB06ZNU2Nj40WfD4fDCofD2R4GAGAIyvr7hNLptD7++GPF4/Fs7woAkGMyXkJPPvmkGhoa1NTUpH//+9/66U9/qlQqperq6kzvCgCQ4zL+67ijR4/qoYceUkdHhyZOnKjZs2dr165dgzp3EwAgN2S8hF599dVM/5VZEwqFAuWKiooGZV/JZNI7g6tz7bXXemfmzJnjnQky6WmQiTuDTBAqBZuMtK+vL9C+fAV583vQu27Hjx/vnTlx4oR3ZiS/NYW54wAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJjJ+ofaDWVjx44NlAsyyeWlPt78ctLptHcGVycajXpnysrKvDNBJiMNMnFn0AlMz5w5450Jcr6eO3fOOxPEjBkzAuWCTCL8n//8J9C+RiquhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZkb0LNpBZkyWJOecd+b48eODsh+cF2TGaUmKxWLemfHjx3tngsxSfezYMe/M0aNHvTOS1NXV5Z05efKkdybITPZB/o1uvfVW74wktbe3e2eCzlw+UnElBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwMyInsA0FAoFyh06dMg78/nnnwfaF4IZN25coFwkEvHOBPm3bW1t9c58+umn3pmWlhbvjCSlUinvTDqd9s4EOd7f//73vTNBJzAN8u/U0dERaF8jFVdCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzIzoCUyZVHT4GjMm2Kn92WefDUomyMSYyWTSO3P27FnvzGDq6enxzpSUlHhnrr/+eu+MJB08eNA7E+RrGsm4EgIAmKGEAABmvEtox44dWrhwoUpLSxUKhbR169YBzzvnVFNTo9LSUuXn52v+/Pk6cOBApsYLABhGvEuou7tb06dP1/r16y/6/HPPPad169Zp/fr12r17t2KxmBYsWKCurq6rHiwAYHjxfvW2qqpKVVVVF33OOacXXnhBa9as0eLFiyVJGzduVElJiTZv3qxHH3306kYLABhWMvqaUFNTk9ra2lRZWdm/LhwO66677tLOnTsvmkmn00qlUgMWAMDIkNESamtrk3ThLZQlJSX9z31VbW2totFo/zJp0qRMDgkAMIRl5e64UCg04LFz7oJ1X1q9erWSyWT/kkgksjEkAMAQlNE3q8ZiMUnnr4ji8Xj/+vb29ku+wSwcDiscDmdyGACAHJHRK6GKigrFYjHV1dX1r+vt7VVDQ4Pmzp2byV0BAIYB7yuhkydP6tChQ/2Pm5qa9MEHH6iwsFA33nijVq5cqbVr12ry5MmaPHmy1q5dq/Hjx+vhhx/O6MABALnPu4Tef/993X333f2PV61aJUmqrq7WH//4Rz311FPq6enR448/ruPHj2vWrFl65513FIlEMjdqAMCwEHLOOetB/P9SqZSi0aj1MJDjLnUjzNcZNcr/N9R9fX3emSH2387MDTfc4J155plnvDPf+973vDOS9POf/9w7889//jPQvoajZDKpgoKCy27D3HEAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADMZ/WRVYCQKMvN2EEFm3g46W3eQXH5+vndmzpw53plbbrnFO/O3v/3NOyNJn3zySaAcrhxXQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMwwgSkGVUFBgXemqKjIOxOJRLwzknTNNdd4Z0aPHh1oX756e3u9MydPngy0r56eHu9MPB73zvzkJz/xzrS0tHhntm7d6p2RpC+++CJQDleOKyEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmmMB0mAkymWbQyT6Li4u9MzfffLN3ZjAnFR01yv/nssGawDTIpKJjxgT7Lx4Oh70zM2bM8M5MnjzZO7NhwwbvzCeffOKdkSTnXKAcrhxXQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMwwgekwU1ZW5p2ZPn16oH0VFBR4Z4JMlnrixAnvTFB5eXmDti9fQY73lClTAu0rGo16Z+68807vTHt7u3cmkUh4Z86cOeOdweDgSggAYIYSAgCY8S6hHTt2aOHChSotLVUoFNLWrVsHPL906VKFQqEBy+zZszM1XgDAMOJdQt3d3Zo+fbrWr19/yW3uvfdetba29i/btm27qkECAIYn7xsTqqqqVFVVddltwuGwYrFY4EEBAEaGrLwmVF9fr+LiYk2ZMkWPPPLIZe+ASafTSqVSAxYAwMiQ8RKqqqrSK6+8ou3bt+v555/X7t27dc899yidTl90+9raWkWj0f5l0qRJmR4SAGCIyvj7hJYsWdL/56lTp+q2225TeXm53nzzTS1evPiC7VevXq1Vq1b1P06lUhQRAIwQWX+zajweV3l5uRobGy/6fDgcVjgczvYwAABDUNbfJ9TZ2alEIqF4PJ7tXQEAcoz3ldDJkyd16NCh/sdNTU364IMPVFhYqMLCQtXU1OiBBx5QPB7XkSNH9Ktf/UpFRUW6//77MzpwAEDu8y6h999/X3fffXf/4y9fz6murtZLL72k/fv3a9OmTTpx4oTi8bjuvvtubdmyJdCcYQCA4c27hObPny/n3CWff/vtt69qQPg/1113nXdm3rx53pmbbrrJOyNJyWTSO9Pb2xtoX4Plcuf2pYwZ4//SaklJiXdm8uTJ3pmgN/kcP37cOxNkotkPP/zQO3Pttdd6Z4qLi70z0vnf/Pjq6+sLtK+RirnjAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmsv7Jqjhv9OjR3plp06Z5ZxYsWOCdaW1t9c5IUkdHh3cmyKzEQWbeDjKztSTl5+d7Z4LMVP3tb3/bO1NYWOid6enp8c5IUiqV8s40Nzd7Z4LMvF1eXu6daWlp8c5I0rFjx7wzQWaXH8m4EgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGCUwHydixY70zP/zhD70z3/nOd7wzhw8f9s5I0tGjR70zQSaEDOL6668PlItGo96ZU6dOeWc+++wz70yQYxd0ctpEIuGdCTLpaVlZmXemoqLCO/PRRx95ZyRp1Ch+Ts82jjAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzTGA6SAoLC70zP/rRj7wz48eP98588cUX3hlJ6urq8s5EIhHvzDXXXOOdCYfD3hlJOn36tHemu7vbO9Pc3OydCTIZ6aeffuqdkaQTJ054ZyZOnOidKSkp8c6MGeP/bSvI1yMFm5wWfrgSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYJTAMYNcq/u8vKyrwzY8eO9c7s3LnTO5NIJLwzknTdddd5Z6699lrvjHPOO9PX1+edkYId897eXu/MsWPHvDN79+71zhw5csQ7I0npdNo7E+QcDzKxb1FRkXcm6CS9Qf5t4YcrIQCAGUoIAGDGq4Rqa2t1++23KxKJqLi4WIsWLdLBgwcHbOOcU01NjUpLS5Wfn6/58+frwIEDGR00AGB48CqhhoYGLVu2TLt27VJdXZ3Onj2rysrKAR/q9dxzz2ndunVav369du/erVgspgULFgT6ADQAwPDmdWPCW2+9NeDxhg0bVFxcrD179mjevHlyzumFF17QmjVrtHjxYknSxo0bVVJSos2bN+vRRx/N3MgBADnvql4TSiaTkv7vDpempia1tbWpsrKyf5twOKy77rrrkndtpdNppVKpAQsAYGQIXELOOa1atUp33HGHpk6dKklqa2uTdOHnxpeUlPQ/91W1tbWKRqP9y6RJk4IOCQCQYwKX0PLly7Vv3z79+c9/vuC5UCg04LFz7oJ1X1q9erWSyWT/EvQ9KwCA3BPozaorVqzQG2+8oR07dgx4g1osFpN0/oooHo/3r29vb7/g6uhL4XBY4XA4yDAAADnO60rIOafly5frtdde0/bt21VRUTHg+YqKCsViMdXV1fWv6+3tVUNDg+bOnZuZEQMAhg2vK6Fly5Zp8+bN+utf/6pIJNL/Ok80GlV+fr5CoZBWrlyptWvXavLkyZo8ebLWrl2r8ePH6+GHH87KFwAAyF1eJfTSSy9JkubPnz9g/YYNG7R06VJJ0lNPPaWenh49/vjjOn78uGbNmqV33nlHkUgkIwMGAAwfXiV0JRNJhkIh1dTUqKamJuiYhrzRo0d7Z/Lz870zQWaa2L9/v3emp6fHOyMFm0gyyOt/p0+f9s6cOXPGOxN0Xy0tLd6ZxsZG70xHR4d35ty5c96ZoDo7O70zR48e9c5c6ianyzl+/Lh3Rgo2eS78MHccAMAMJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMBMoE9Whb8gszPv27fPO/P55597Z4LMCh40l06nvTNdXV3emSAzOktSc3OzdybILNpBzoeh7tSpU96ZRCLhnTl27Jh35osvvvDOYHBwJQQAMEMJAQDMUEIAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAME5gG0NfX5505ceKEd+bDDz/0zpw5c8Y7EwqFvDOSlEqlvDNBJpI8fPiwd6a9vd07IwWbYBXBBZn8FcMLV0IAADOUEADADCUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMMIFpAOfOnfPOJBIJ70yQCUJHjRq8nyuCTPbZ1dXlnTl9+rR3BkBu4EoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGSYwHSTd3d2DkgGAXMKVEADADCUEADDjVUK1tbW6/fbbFYlEVFxcrEWLFungwYMDtlm6dKlCodCAZfbs2RkdNABgePAqoYaGBi1btky7du1SXV2dzp49q8rKygteu7j33nvV2trav2zbti2jgwYADA9eNya89dZbAx5v2LBBxcXF2rNnj+bNm9e/PhwOKxaLZWaEAIBh66peE0omk5KkwsLCAevr6+tVXFysKVOm6JFHHlF7e/sl/450Oq1UKjVgAQCMDCHnnAsSdM7pvvvu0/Hjx/Xee+/1r9+yZYuuueYalZeXq6mpSb/+9a919uxZ7dmzR+Fw+IK/p6amRr/5zW+CfwUAgCEpmUyqoKDg8hu5gB5//HFXXl7uEonEZbdraWlxeXl57i9/+ctFnz99+rRLJpP9SyKRcJJYWFhYWHJ8SSaTX9slgd6sumLFCr3xxhvasWOHysrKLrttPB5XeXm5GhsbL/p8OBy+6BUSAGD48yoh55xWrFih119/XfX19aqoqPjaTGdnpxKJhOLxeOBBAgCGJ68bE5YtW6Y//elP2rx5syKRiNra2tTW1qaenh5J0smTJ/Xkk0/qX//6l44cOaL6+notXLhQRUVFuv/++7PyBQAAcpjP60C6xO/9NmzY4Jxz7tSpU66ystJNnDjR5eXluRtvvNFVV1e75ubmK95HMpk0/z0mCwsLC8vVL1fymlDgu+OyJZVKKRqNWg8DAHCVruTuOOaOAwCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYoYQAAGYoIQCAGUoIAGCGEgIAmKGEAABmKCEAgBlKCABghhICAJihhAAAZighAIAZSggAYIYSAgCYGXIl5JyzHgIAIAOu5Pv5kCuhrq4u6yEAADLgSr6fh9wQu/To6+tTS0uLIpGIQqHQgOdSqZQmTZqkRCKhgoICoxHa4zicx3E4j+NwHsfhvKFwHJxz6urqUmlpqUaNuvy1zphBGtMVGzVqlMrKyi67TUFBwYg+yb7EcTiP43Aex+E8jsN51schGo1e0XZD7tdxAICRgxICAJjJqRIKh8N6+umnFQ6HrYdiiuNwHsfhPI7DeRyH83LtOAy5GxMAACNHTl0JAQCGF0oIAGCGEgIAmKGEAABmcqqEXnzxRVVUVGjcuHGaOXOm3nvvPeshDaqamhqFQqEBSywWsx5W1u3YsUMLFy5UaWmpQqGQtm7dOuB555xqampUWlqq/Px8zZ8/XwcOHLAZbBZ93XFYunTpBefH7NmzbQabJbW1tbr99tsViURUXFysRYsW6eDBgwO2GQnnw5Uch1w5H3KmhLZs2aKVK1dqzZo12rt3r+68805VVVWpubnZemiD6tZbb1Vra2v/sn//fushZV13d7emT5+u9evXX/T55557TuvWrdP69eu1e/duxWIxLViwYNjNQ/h1x0GS7r333gHnx7Zt2wZxhNnX0NCgZcuWadeuXaqrq9PZs2dVWVmp7u7u/m1GwvlwJcdBypHzweWI7373u+6xxx4bsO6WW25xv/zlL41GNPiefvppN336dOthmJLkXn/99f7HfX19LhaLuWeffbZ/3enTp100GnW/+93vDEY4OL56HJxzrrq62t13330m47HS3t7uJLmGhgbn3Mg9H756HJzLnfMhJ66Eent7tWfPHlVWVg5YX1lZqZ07dxqNykZjY6NKS0tVUVGhBx98UIcPH7Yekqmmpia1tbUNODfC4bDuuuuuEXduSFJ9fb2Ki4s1ZcoUPfLII2pvb7ceUlYlk0lJUmFhoaSRez589Th8KRfOh5wooY6ODp07d04lJSUD1peUlKitrc1oVINv1qxZ2rRpk95++229/PLLamtr09y5c9XZ2Wk9NDNf/vuP9HNDkqqqqvTKK69o+/btev7557V7927dc889SqfT1kPLCuecVq1apTvuuENTp06VNDLPh4sdByl3zochN4v25Xz1ox2ccxesG86qqqr6/zxt2jTNmTNH3/jGN7Rx40atWrXKcGT2Rvq5IUlLlizp//PUqVN12223qby8XG+++aYWL15sOLLsWL58ufbt26d//OMfFzw3ks6HSx2HXDkfcuJKqKioSKNHj77gJ5n29vYLfuIZSSZMmKBp06apsbHReihmvrw7kHPjQvF4XOXl5cPy/FixYoXeeOMNvfvuuwM++mWknQ+XOg4XM1TPh5woobFjx2rmzJmqq6sbsL6urk5z5841GpW9dDqtjz/+WPF43HooZioqKhSLxQacG729vWpoaBjR54YkdXZ2KpFIDKvzwzmn5cuX67XXXtP27dtVUVEx4PmRcj583XG4mCF7PhjeFOHl1VdfdXl5ee4Pf/iD++ijj9zKlSvdhAkT3JEjR6yHNmieeOIJV19f7w4fPux27drlfvzjH7tIJDLsj0FXV5fbu3ev27t3r5Pk1q1b5/bu3ev+97//Oeece/bZZ100GnWvvfaa279/v3vooYdcPB53qVTKeOSZdbnj0NXV5Z544gm3c+dO19TU5N599103Z84cd8MNNwyr4/CLX/zCRaNRV19f71pbW/uXU6dO9W8zEs6HrzsOuXQ+5EwJOefcb3/7W1deXu7Gjh3rZsyYMeB2xJFgyZIlLh6Pu7y8PFdaWuoWL17sDhw4YD2srHv33XedpAuW6upq59z523KffvppF4vFXDgcdvPmzXP79++3HXQWXO44nDp1ylVWVrqJEye6vLw8d+ONN7rq6mrX3NxsPeyMutjXL8lt2LChf5uRcD583XHIpfOBj3IAAJjJideEAADDEyUEADBDCQEAzFBCAAAzlBAAwAwlBAAwQwkBAMxQQgAAM5QQAMAMJQQAMEMJAQDMUEIAADP/Dy6M9dpd/8pjAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.imshow(pixels, cmap='gray')\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}