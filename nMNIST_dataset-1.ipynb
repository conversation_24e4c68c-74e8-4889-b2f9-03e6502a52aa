{"cells": [{"cell_type": "code", "execution_count": 1, "id": "005ce0cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["<torch._C.Generator at 0x106a43f10>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader\n", "from torchvision import datasets, transforms\n", "\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "torch.manual_seed(88)"]}, {"cell_type": "code", "execution_count": 2, "id": "18aecf25", "metadata": {}, "outputs": [], "source": ["# Import torch.utils.data and csv to read the customized dataset for Assignment 2 Part 2\n", "import torch.utils.data as data\n", "import csv\n", "\n", "# Training Dataset\n", "class nMNIST_Dataset(data.Dataset):\n", "    \n", "    def __init__(self, size=60000):\n", "        \"\"\"\n", "        Inputs:\n", "            size - number of digit image, n-MNIST train has 60000\n", "        \"\"\"\n", "        super().__init__()\n", "        self.size = size\n", "        self.load_data()\n", "        self.T = transforms.ToTensor()  # Transform from numpy to torch tensor\n", "\n", "    # Load all data: assuming your dataset is downloaded and put in n-MNIST\n", "    def load_data(self):\n", "        nTrain = 60000\n", "        img_data = np.zeros((nT<PERSON>,28,28))\n", "        label_data = np.zeros((nTrain, 10))\n", "        with open('./n-MNIST/trainx.csv', 'r') as csv_file:\n", "            csvreader = csv.reader(csv_file)\n", "            for i,data in enumerate(csvreader):\n", "                img = np.array(data, dtype='int64')\n", "                img_data[i] = img.reshape((28,28))\n", "        with open('./n-MNIST/trainy.csv', 'r') as csv_file:\n", "            csvreader = csv.reader(csv_file)\n", "            for i,data in enumerate(csvreader):\n", "                label = np.array(data, dtype='int64')\n", "                label_data[i] = label\n", "        \n", "        self.data = img_data\n", "        self.label = label_data\n", "        \n", "    def __len__(self):\n", "        return self.size\n", "    \n", "    def __getitem__(self, idx):\n", "        data_point = self.data[idx]\n", "        data_label = self.label[idx]\n", "        data_point = self.T(data_point).to(torch.float32)\n", "        data_label = torch.tensor(data_label).argmax()\n", "\n", "        return data_point, data_label"]}, {"cell_type": "code", "execution_count": 3, "id": "29782560", "metadata": {}, "outputs": [], "source": ["# Test Dataset\n", "class nMNIST_Dataset_Test(data.Dataset):\n", "    \n", "    def __init__(self, size=10000):\n", "        \"\"\"\n", "        Inputs:\n", "            size - number of digit image, n-MNIST test has 10000\n", "        \"\"\"\n", "        super().__init__()\n", "        self.size = size\n", "        self.load_data()\n", "        self.T = transforms.ToTensor() # Transform from numpy to torch tensor\n", "\n", "    def load_data(self):\n", "        nTest = 10000\n", "        img_data = np.zeros((nTest,28,28))\n", "        label_data = np.zeros((nTest, 10))\n", "        with open('./n-MNIST/testx.csv', 'r') as csv_file:\n", "            csvreader = csv.reader(csv_file)\n", "            for i,data in enumerate(csvreader):\n", "                img = np.array(data, dtype='int64')\n", "                img_data[i] = img.reshape((28,28))\n", "        with open('./n-MNIST/testy.csv', 'r') as csv_file:\n", "            csvreader = csv.reader(csv_file)\n", "            for i,data in enumerate(csvreader):\n", "                label = np.array(data, dtype='int64')\n", "                label_data[i] = label\n", "        \n", "        self.data = img_data\n", "        self.label = label_data\n", "        \n", "    def __len__(self):\n", "        return self.size\n", "    \n", "    def __getitem__(self, idx):\n", "        data_point = self.data[idx]\n", "        data_label = self.label[idx]\n", "        data_point = self.T(data_point).to(torch.float32)\n", "        data_label = torch.tensor(data_label).argmax()   # Convert one-hot label to integer one for test \n", "\n", "        return data_point, data_label"]}, {"cell_type": "code", "execution_count": null, "id": "e6b17122-5c14-4179-9fae-e2f0505424a4", "metadata": {}, "outputs": [], "source": ["# Load the train and test dataset \n", "train = nMNIST_Dataset()\n", "train_loader = DataLoader(train, batch_size=100, shuffle=True)\n", "test = nMNIST_Dataset_Test()\n", "test_loader = DataLoader(test, batch_size=500, shuffle=False)"]}, {"cell_type": "code", "execution_count": null, "id": "ddb2e48f-b2e8-4f32-9a71-d6cc4e1f712f", "metadata": {}, "outputs": [], "source": ["# Test the dataset is correctly created\n", "print(train[0][0].size(), train[0][0].dtype, len(train_loader))\n", "print(train[0][1], train[0][1].dtype)\n", "print(test[0][0].size(), test[0][0].dtype, len(test_loader))\n", "print(test[0][1], test[0][1].dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "d6aaa083-5360-4c82-92c5-6a08c37ea95e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}